package game

import (
	"casrv/server/common/config"
	"casrv/server/common/pb"
	rds "casrv/server/common/redis"
	camptype "casrv/server/game/enum/camp_type"
	ut "casrv/utils"
	"casrv/utils/array"
	"encoding/json"

	"github.com/huyangv/vmqant/log"
)

// 游戏数据
type GameData struct {
	Player      *PlayerInfo    `json:"player"`      //自己的信息
	Encounter   *EncounterInfo `json:"encounter"`   //遭遇信息
	SoldierDeck []int32        `json:"soldierDeck"` //武将卡组
	MapPaths    []int32        `json:"mapPaths"`    //走过的路径
	CreateTime  int64          `json:"createTime"`  //创建时间
}

func (this *GameData) ToPb() *pb.GameData {
	return &pb.GameData{
		Player:    this.Player.ToPb(),
		Encounter: this.Encounter.ToPb(),
		MapPaths:  array.Clone(this.MapPaths),
	}
}

// 获取游戏数据
func GetGameData(uid string) *GameData {
	jsonStr, err := rds.RdsHGet(rds.RDS_GAME_DATA_KEY+uid, "data")
	if err != nil || jsonStr == "" {
		return nil
	}
	// 将JSON字符串反序列化为GameData对象
	gameData := new(GameData)
	if err := json.Unmarshal([]byte(jsonStr), gameData); err != nil {
		log.Error("GetGameData unmarshal error: %v", err)
		return nil
	} else if gameData.Encounter == nil {
		gameData.Encounter = new(EncounterInfo)
	}
	return gameData
}

// 保存游戏数据到redis
func SaveGameData(uid string, data *GameData) error {
	// 将GameData序列化为JSON字符串
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		log.Error("SaveGameData marshal error: %v", err)
		return err
	}
	// 保存到redis hash中
	return rds.RdsHSet(rds.RDS_GAME_DATA_KEY+uid, "data", string(jsonBytes))
}

// 创建游戏信息
func CreateGame(uid string, nickname string, roleId int32, campType int32) *GameData {
	data := &GameData{CreateTime: ut.Now(), MapPaths: []int32{}, Encounter: new(EncounterInfo)}
	// 自己的数据
	data.Player = NewPlayer(uid, nickname, roleId, campType)
	// 初始化开始的遭遇
	data.ResetEncounter(MAP_NODE_TYPE_TAVERN)
	// 添加路径 默认添加第一个路径
	data.MapPaths = append(data.MapPaths, 0)
	// 小时增加
	data.Player.Hour += 1
	// 保存到redis
	SaveGameData(uid, data)
	return data
}

// 进行下一天
func (this *GameData) NextDay() {
	// 添加遭遇

	// 增加天数和金币
	player := this.Player
	player.Day += 1
	player.Hour = 0 //重置小时数
	player.Gold += player.Earnings
}

// 清理遭遇信息
func (this *GameData) CleanEncounterData() {
	this.Encounter.CleanData()
}

// 重置遭遇
func (this *GameData) ResetEncounter(tp int32) {
	this.CleanEncounterData()
	this.Encounter.Type = tp
	switch tp {
	case MAP_NODE_TYPE_ENEMY_PLAYER:
		// this.Encounter = CreatePlayer()
	case MAP_NODE_TYPE_ENEMY_BATTLE:
		this.Encounter.Tavern = this.CreateTavern(3)
	case MAP_NODE_TYPE_TAVERN:
		this.Encounter.Tavern = this.CreateTavern(3)
	}
}

// 创建酒馆信息
func (this *GameData) CreateTavern(count int) *TavernInfo {
	player := this.Player
	day, campType := player.Day, player.CampType
	// 满品质的英雄map
	maxQualityHeroMap := player.GetMaxQualityHeroMap()
	// 获取配置列表
	heros, totalWeight := []map[string]any{}, 0
	datas := config.GetJson("heroBase").Datas
	for _, m := range datas {
		if camp := ut.Int32(m["camp_type"]); camp != campType && camp != camptype.QUN {
			continue //不同阵营
		} else if day < ut.Int32(m["appear_day"]) {
			continue //出场天数不够
		} else if maxQualityHeroMap[ut.Int32(m["id"])] != nil {
			continue //已经满级了
		}
		heros = append(heros, m)           //添加英雄列表信息
		totalWeight += ut.Int(m["weight"]) //总权重信息
	}

	ut.RandomIndexByWeightHasTotal(heros, totalWeight)

	return &TavernInfo{Heros: RandomHeros(heros, 3)} //随机3个英雄
}
