package game

// 地图节点类型
const (
	_                          = iota //开始 0
	MAP_NODE_TYPE_ENEMY_PLAYER        //玩家 1
	MAP_NODE_TYPE_ENEMY_BATTLE        //战斗 2
	MAP_NODE_TYPE_TAVERN              //酒馆 3
	MAP_NODE_TYPE_HIERON              //神庙 4
	MAP_NODE_TYPE_RAND                //随机事件 5
)

// 区域类型
const (
	_                 = iota
	AREA_TYPE_BATTLE  //战斗区域 1
	AREA_TYPE_PREPARE //备战区域 2
	AREA_TYPE_SHOP    //商店区域 3
	AREA_TYPE_ENEMY   //敌人区域 4
)

// 英雄属性类型
const (
	NONE                  = iota
	HERO_ATTR_TYPE_HP     //生命 1
	HERO_ATTR_TYPE_ATTACK //攻击 2
	HERO_ATTR_TYPE_EFFECT //效果 3
)
