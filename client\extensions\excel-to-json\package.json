{"package_version": 2, "name": "excel-to-json", "version": "1.0.0", "author": "wohow", "editor": ">=3.8.6", "scripts": {"preinstall": "node ./scripts/preinstall.js", "build": "npx tsc"}, "description": "Excel转Json工具", "main": "./dist/main.js", "dependencies": {"fs-extra": "^10.0.0", "chokidar": "^2.0.2", "json-beautifully": "^1.0.3", "node-xlsx": "^0.11.2", "opencc-js": "^1.0.5", "uglify-js": "^3.3.14", "vue": "^2.6.14"}, "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/fs-extra": "^9.0.5", "@types/node": "^18.17.1", "typescript": "^5.8.2"}, "panels": {"default": {"title": "Excel转Json", "type": "dockable", "main": "dist/panels/default/simple", "size": {"min-width": 600, "min-height": 700, "width": 600, "height": 750}}}, "contributions": {"menu": [{"path": "自定义扩展/Excel转Json", "label": "open", "message": "open-panel"}], "messages": {"open-panel": {"methods": ["openPanel"]}}}}