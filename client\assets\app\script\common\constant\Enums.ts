
// 登陆类型
enum LoginType {
    NONE = 'none',
    GUEST = 'guest', //游客
    ACCOUNT = 'account', //账号
    WX = 'wx', //微信
    GOOGLE = 'google',
    APPLE = 'apple',
    FACEBOOK = 'facebook',
    TWITTER = 'twitter',
    LINE = 'line',
}

// 登录状态
enum LoginState {
    SUCCEED,            //成功
    FAILURE,            //登录失败
    NOT_ACCOUNT_TOKEN,  //本地还没有token 显示登录按钮
    VERSION_TOOLOW,     //版本过低
    VERSION_TOOTALL,    //版本过高
    BANACCOUNT_TIME,    //封禁时间
    QUEUE_UP,           //需要排队
    ENTER_GAME,         //有游戏数据
}

// 服务器的通知类型
enum NotifyType {
    NONE,
}

// 英雄状态
enum HeroState {
    NONE,
    STAND, //待机 以下都表示战斗中
    ATTACK, //攻击
    HIT, //受击
}

// 地图节点类型
enum MapNodeType {
    NONE,
    ENEMY_PLAYER, //玩家
    ENEMY_BATTLE, //战斗
    TAVERN, //酒馆
    HIERON, //神庙
    RAND, //随机事件
}

// 拖拽触摸类型
enum DragTouchType {
    NONE,
    DRAG_BEGIN, //拖动开始
    DRAG_MOVE, //拖拽移动
    DRAG_END, //拖动结束
    CLICK, //点击
}

// 区域类型
enum AreaType {
    NONE,
    BATTLE, //战斗区域
    PREPARE, //备战区域
    SHOP, //商店区域
    ENEMY, //敌人区域
}

// 阵营
enum CampType {
    QUN,    //群
    WEI,    //魏
    SHU,    //蜀
    WU,     //吴
    QIN,    //秦
    HAN,    //汉
    TANG,   //唐
    MING,   //明
}

// 英雄属性类型
enum HeroAttrType {
    NONE,
    HP, //生命 1
    ATTACK, //攻击 2
    EFFECT, //效果 3
}

// 英雄效果类型
enum HeroEffectType {
    NONE,
}

export {
    LoginType,
    LoginState,
    NotifyType,
    HeroState,
    MapNodeType,
    DragTouchType,
    AreaType,
    CampType,
    HeroAttrType,
    HeroEffectType,
}