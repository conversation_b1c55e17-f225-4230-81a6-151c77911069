import { Vec3 } from "cc";

// 提示框参数选择
type AlertOpts = {
    params?: any[]; //文本参数
    showTime?: number; //显示时间
    cb?: Function;
}

// 对话框选择参数
type MessageBoxOpts = {
    title?: string,
    ok?: Function;
    cancel?: Function;
    okText?: string,
    cancelText?: string,
    params?: any[]; //文本参数
    mask?: boolean; //是否显示mask
    lockClose?: boolean; //禁止点击空白关闭
    clickButtonClose?: boolean; //点击按钮是否关闭界面
}

type AnimFrameInfo = {
    name: string;
    interval: number;
    loop: boolean;
    frameIndexs: string[];
}

// 帧动画配置信息
type FrameAnimConfInfo = {
    url: string;
    offset: Vec3;
    anims: AnimFrameInfo[];
}

// 行为树节点配置信息
type BehaviorNodeConfInfo = {
    type: string;
    cls: string;
    children: number[];
    params?: any;
}

// 英雄基础配置
type HeroBaseJItem = {
    id: number;
    camp_type: number; //阵营
    quality: number;
}

// 英雄属性配置
type HeroAttrJItem = {
    id: number;
    hp: number;
    attack: number;
    effects: string; //效果列表
}

export type {
    AlertOpts,
    MessageBoxOpts,
    AnimFrameInfo,
    FrameAnimConfInfo,
    BehaviorNodeConfInfo,
    HeroBaseJItem,
    HeroAttrJItem,
}