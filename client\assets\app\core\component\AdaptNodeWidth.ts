import { _decorator, Component, v3 } from "cc";

const { ccclass, property, menu } = _decorator;

/**
 * 自动适宽度
 */
@ccclass
@menu('自定义组件/AdaptNodeWidth')
export default class AdaptNodeWidth extends Component {

    @property
    private maxWidth: number = 0

    private preWidth: number = 0

    public setMaxWidth(val: number) {
        if (this.maxWidth !== val) {
            this.maxWidth = val
            this.do()
        }
    }

    update() {
        if (this.preWidth !== this.node.Width) {
            this.do()
        }
    }

    private do() {
        const w = this.preWidth = this.node.Width
        const scale = w > this.maxWidth ? this.maxWidth / w : 1
        this.node.scale = v3(scale, scale, scale)
    }
}