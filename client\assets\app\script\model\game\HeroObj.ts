import { Hero<PERSON>ttrJItem, HeroBaseJItem } from "../../common/constant/DataType"
import { AreaType, HeroAttrType, HeroState } from "../../common/constant/Enums"
import HeroEffectObj from "./HeroEffectObj"
import HeroStateObj from "./HeroStateObj"

// 一个英雄
export default class HeroObj {

    public uid: string = ''
    public id: number = 0
    public quality: number = 0
    public attrs: { arr: number[] }[] = [] //属性 {type, id, value} type: 0.基础属性 1.效果
    public cost: number = 0 //费用
    public areaType: AreaType = AreaType.NONE //所在区域 1.战斗区域 2.备战区域 3.遭遇区域
    public index: number = 0 //所在位置
    public skinId: number = 0

    public baseJson: HeroBaseJItem = null
    public attrJson: HeroAttrJItem = null

    public hp: number = 0
    public attack: number = 0
    public effects: HeroEffectObj[] = [] //效果列表
    public state: HeroStateObj = null //状态

    public init(data: any) {
        this.uid = data.uid
        this.id = data.id
        this.quality = data.quality
        this.attrs = data.attrs || []
        this.cost = data.cost
        this.areaType = data.areaType
        this.index = data.index
        this.initJson()
        return this
    }

    private initJson() {
        this.baseJson = assetsMgr.getJsonData('heroBase', this.id)
        this.attrJson = assetsMgr.getJsonData('heroAttr', this.id * 100 + this.quality)
        this.updateAttrs()
    }

    private updateAttrs() {
        this.hp = 0
        this.attack = 0
        this.effects.length = 0
        this.attrs.forEach(m => {
            const [type, id, value] = m.arr
            if (type === HeroAttrType.HP) {
                this.hp += value
            } else if (type === HeroAttrType.ATTACK) {
                this.attack += value
            } else if (type === HeroAttrType.EFFECT) {
                this.effects.push(new HeroEffectObj().init(id, value))
            }
        })
    }

    public getViewId() { return this.skinId || this.id }
    public getPrefabUrl() { return 'hero/HERO_' + this.getViewId() }

    // 是否可以拖动
    public isCanDrag() {
        return this.areaType === AreaType.BATTLE || this.areaType === AreaType.PREPARE || this.areaType === AreaType.SHOP
    }

    public isDie() {
        return this.hp <= 0
    }

    public changeState(state: HeroState, data?: any) {
        if (!this.state) {
            this.state = new HeroStateObj()
        }
        this.state.init(state, data)
        // log('changeState', this.uid, this.index, HeroState[state], data)
    }
}