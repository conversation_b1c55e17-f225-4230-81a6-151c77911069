import { _decorator, Component, Enum, Node, v2, Vec2, Widget } from "cc";

const { ccclass, property, menu } = _decorator;

enum AdaptType {
    NONE,
    WIDTH, //只适配宽度
    HEIGHT, //只适配高度
}

/**
 * 适配节点大小
 */
@ccclass
@menu('自定义组件/AdaptNodeSize')
export default class AdaptNodeSize extends Component {

    @property(Node)
    private target: Node = null
    @property({ type: Enum(AdaptType) })
    private adaptType: AdaptType = AdaptType.NONE
    @property(Vec2)
    private addOffset: Vec2 = v2()
    @property(Vec2)
    private minSize: Vec2 = v2()
    @property()
    private isUpdateWidget: boolean = false

    private preScaleX: number = -1
    private preScaleY: number = -1
    private preWidth: number = -1
    private preHeight: number = -1

    update() {
        if (!this.target) {
            return
        }
        const sx = this.target.scale.x, sy = this.target.scale.y
        const tw = this.target.Width, th = this.target.Height
        if (this.preScaleX !== sx || this.preScaleY !== sy || this.preWidth !== tw || this.preHeight !== th) {
            this.preScaleX = sx
            this.preScaleY = sx
            this.preWidth = tw
            this.preHeight = th
            let width = this.node.Width, height = this.node.Height
            if (this.adaptType === AdaptType.NONE) {
                width = tw * sx + this.addOffset.x
                height = th * sy + this.addOffset.y
            } else if (this.adaptType === AdaptType.WIDTH) {
                width = tw * sx + this.addOffset.x
            } else if (this.adaptType === AdaptType.HEIGHT) {
                height = th * sy + this.addOffset.y
            }
            if (this.minSize.x > 0) {
                width = Math.max(this.minSize.x, width)
            }
            if (this.minSize.y > 0) {
                height = Math.max(this.minSize.y, height)
            }
            this.node.Width = width
            this.node.Height = height
            if (this.isUpdateWidget) {
                this.node.children.forEach(m => m.Component(Widget)?.updateAlignment())
            }
        }
    }
}